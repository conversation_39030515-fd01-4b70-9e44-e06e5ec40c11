import React, { useState, useEffect } from 'react';
import { ArrowLeft, Plus, Search, Edit, Trash2, Check, AlertCircle } from 'lucide-react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { Category } from '../types';
import config from '../config';
import { fetchWithSession, getCurrentUser, buildApiUrl } from '../utils/api';

const CategoryManagement: React.FC = () => {
  const navigate = useNavigate();
  const [categories, setCategories] = useState<Category[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [searchParams, setSearchParams] = useSearchParams();

  // Get current user info
  const currentUser = getCurrentUser();
  const isBranchUser = currentUser?.role === 'branch';

  const showSuccess = (message: string) => {
    setAlertMessage(message);
    setShowSuccessAlert(true);
    setTimeout(() => {
      setShowSuccessAlert(false);
    }, 3000);
  };

  const showError = (message: string) => {
    setAlertMessage(message);
    setShowErrorAlert(true);
    setTimeout(() => {
      setShowErrorAlert(false);
    }, 3000);
  };

  // Check for success message from URL parameters
  useEffect(() => {
    const successMessage = searchParams.get('success');
    if (successMessage) {
      showSuccess(successMessage);
      // Remove the success parameter from URL
      setSearchParams({});
    }
  }, [searchParams, setSearchParams]);

  // Fetch categories from API
  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoading(true);
      try {
        const url = buildApiUrl(`${config.apiUrl}/categories`);
        const response = await fetchWithSession(url);
        if (response && response.ok) {
          const data = await response.json();
          // Transform data to match our interface
          const formattedCategories = data.map((category: any) => ({
            id: category.id.toString(),
            value: category.value,
            label: category.label,
            icon: category.icon,
            count: category.count || 0,
            userId: category.user_id
          }));
          // Data is already sorted by server
          setCategories(formattedCategories);
        }
      } catch (error) { } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const filteredCategories = categories.filter(category =>
    category.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleDeleteClick = (category: Category) => {
    setCategoryToDelete(category);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (categoryToDelete) {
      // Don't allow deleting the "all" category
      if (categoryToDelete.value === 'all') {
        showError('Kategori "All Menu" tidak dapat dihapus');
        setIsDeleteModalOpen(false);
        setCategoryToDelete(null);
        return;
      }

      try {
        const url = buildApiUrl(`${config.apiUrl}/categories/${categoryToDelete.id}`);
        const response = await fetchWithSession(url, {
          method: 'DELETE'
        });

        if (response && response.ok) {
          const updatedCategories = categories.filter(c => c.id !== categoryToDelete.id);
          setCategories(updatedCategories);
          showSuccess('Kategori berhasil dihapus');
        } else {
          const errorData = await response?.json();
          showError(errorData?.error || 'Gagal menghapus kategori');
        }
      } catch (error) {
        showError('Terjadi kesalahan saat menghapus kategori');
      }

      setIsDeleteModalOpen(false);
      setCategoryToDelete(null);
    }
  };

  const handleAddCategory = () => {
    navigate('/category/new');
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-3">
        <div className="flex items-center gap-3">
          <Link to="/dashboard-summary" className="p-2 rounded-full hover:bg-neutral-100">
            <ArrowLeft size={20} />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-primary-800">Manajemen Kategori</h1>
            {isBranchUser && (
              <p className="text-sm text-neutral-600 mt-1">
                Mengelola kategori untuk cabang: <span className="font-medium">{currentUser?.name}</span>
              </p>
            )}
          </div>
        </div>
        <button
          onClick={handleAddCategory}
          className="flex items-center justify-center gap-2 bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 w-full sm:w-auto"
        >
          <Plus size={18} />
          <span>Tambah Kategori</span>
        </button>
      </div>

      {/* Success Alert */}
      {showSuccessAlert && (
        <div className="bg-green-100 border border-green-200 text-green-800 rounded-lg p-4 mb-6 flex items-center">
          <Check size={20} className="mr-2" />
          <span>{alertMessage}</span>
        </div>
      )}

      {/* Error Alert */}
      {showErrorAlert && (
        <div className="bg-red-100 border border-red-200 text-red-800 rounded-lg p-4 mb-6 flex items-center">
          <AlertCircle size={20} className="mr-2" />
          <span>{alertMessage}</span>
        </div>
      )}

      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="p-4 border-b border-neutral-200">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-neutral-400" />
            </div>
            <input
              type="text"
              placeholder="Cari kategori..."
              className="pl-10 p-2 border border-neutral-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              autoComplete="off"
            />
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
            <p className="mt-2 text-neutral-600">Memuat data kategori...</p>
          </div>
        ) : filteredCategories.length === 0 ? (
          <div className="text-center py-8 text-neutral-500">
            <p>Tidak ada kategori yang ditemukan</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-neutral-50">
                <tr className="border-b border-neutral-200">
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Icon</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Nama Kategori</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Kode</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Jumlah Produk</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {filteredCategories.map((category) => (
                  <tr key={category.id} className="border-b border-neutral-100 hover:bg-neutral-50">
                    <td className="px-4 py-3">
                      <div className="w-8 h-8 flex items-center justify-center">
                        {category.icon && (category.icon.startsWith('/uploads/') || category.icon.startsWith('http')) ? (
                          <img
                            src={category.icon.startsWith('/uploads/') ? `${config.imageBaseUrl}${category.icon}` : category.icon}
                            alt={category.label}
                            className="w-8 h-8 object-cover rounded"
                          />
                        ) : (
                          <div className="text-2xl">{category.icon}</div>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3 font-medium text-neutral-800">{category.label}</td>
                    <td className="px-4 py-3 text-neutral-600">{category.value}</td>
                    <td className="px-4 py-3 text-neutral-600">{category.count || 0}</td>
                    <td className="px-4 py-3">
                      <div className="flex justify-end gap-2">
                        <Link
                          to={`/category/edit/${category.id}`}
                          className="p-1 text-primary-600 hover:text-primary-800 rounded-full hover:bg-primary-50"
                        >
                          <Edit size={18} />
                        </Link>
                        <button
                          onClick={() => handleDeleteClick(category)}
                          className="p-1 text-red-600 hover:text-red-800 rounded-full hover:bg-red-50"
                          disabled={category.value === 'all'}
                        >
                          <Trash2 size={18} className={category.value === 'all' ? 'opacity-50' : ''} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-neutral-900 mb-4">Konfirmasi Hapus</h3>
            <p className="text-neutral-700 mb-6">
              Apakah Anda yakin ingin menghapus kategori <span className="font-semibold">{categoryToDelete?.label}</span>? Tindakan ini tidak dapat dibatalkan.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 border border-neutral-300 rounded-md hover:bg-neutral-50"
              >
                Batal
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Hapus
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CategoryManagement;






