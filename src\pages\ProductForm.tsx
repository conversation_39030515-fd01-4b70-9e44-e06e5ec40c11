import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Save, Upload, X } from 'lucide-react';
import { Category } from '../types';
import config from '../config';
import { fetchWithSession, getCurrentUser } from '../utils/api';

interface ProductFormData {
  name: string;
  price: number;
  formattedPrice: string;
  costPrice: number;
  formattedCostPrice: string;
  image: string;
  category: string;
  categoryLabel: string;
  userId: string | undefined;
}

const ProductForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = id !== undefined;
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    price: 0,
    formattedPrice: '',
    costPrice: 0,
    formattedCostPrice: '',
    image: '',
    category: '',
    categoryLabel: '',
    userId: undefined
  });
  const [categories, setCategories] = useState<Category[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadMethod, setUploadMethod] = useState<'url' | 'file'>('url');

  const formatNumberInput = (value: string | number): string => {
    // Handle null, undefined, or empty values
    if (value === null || value === undefined || value === '') {
      return '';
    }

    // Convert to number first to handle decimal strings properly
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;

    // If it's not a valid number, return empty string
    if (isNaN(numericValue)) {
      return '';
    }

    // Convert to integer (remove decimal places)
    const integerValue = Math.floor(numericValue);

    // Convert to string and format with thousands separator
    return integerValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // Dapatkan user_id dari user yang sedang login
        const currentUser = getCurrentUser();
        const userId = currentUser?.id;

        // Tambahkan user_id sebagai query parameter
        const response = await fetchWithSession(`${config.apiUrl}/categories?user_id=${userId}`);

        if (response && response.ok) {
          const data = await response.json();
          // Filter out the "all" category
          const filteredCategories = data
            .filter((cat: any) => cat.value !== 'all')
            .map((cat: any) => ({
              id: cat.id.toString(),
              value: cat.value,
              label: cat.label,
              icon: cat.icon,
              count: cat.count || 0
            }));
          setCategories(filteredCategories);
        }
      } catch (error) { }
    };

    fetchCategories();
  }, []);

  // Fetch product data if in edit mode
  useEffect(() => {
    if (isEditMode) {
      const fetchProduct = async () => {
        setIsLoading(true);
        try {
          // Dapatkan user_id dari user yang sedang login
          const currentUser = getCurrentUser();
          const userId = currentUser?.id;

          const response = await fetchWithSession(`${config.apiUrl}/products/${id}?user_id=${userId}`);
          if (response && response.ok) {
            const productData = await response.json();

            // Parse price and cost_price properly
            // Database returns decimal as string like "30000.00" or number
            const price = Math.floor(parseFloat(productData.price || '0'));
            const costPrice = Math.floor(parseFloat(productData.cost_price || '0'));

            setFormData({
              name: productData.name,
              price: price,
              formattedPrice: formatNumberInput(price),
              costPrice: costPrice,
              formattedCostPrice: formatNumberInput(costPrice),
              image: productData.image,
              category: productData.category,
              categoryLabel: productData.category_label,
              userId: productData.user_id
            });

            // Tentukan metode upload berdasarkan URL gambar
            if (productData.image && productData.image.startsWith('/uploads/')) {
              setUploadMethod('file');
            } else {
              setUploadMethod('url');
            }
          } else {
            navigate('/products');
          }
        } catch (error) {
          navigate('/products');
        } finally {
          setIsLoading(false);
        }
      };

      fetchProduct();
    }
  }, [id, isEditMode, navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === 'price') {
      // Extract only numeric characters from input
      const numericValue = parseInt(value.replace(/\D/g, '')) || 0;
      // Format the numeric value for display
      const formattedValue = formatNumberInput(numericValue);
      setFormData(prev => ({
        ...prev,
        price: numericValue,
        formattedPrice: formattedValue
      }));
    } else if (name === 'costPrice') {
      // Extract only numeric characters from input
      const numericValue = parseInt(value.replace(/\D/g, '')) || 0;
      // Format the numeric value for display
      const formattedValue = formatNumberInput(numericValue);
      setFormData(prev => ({
        ...prev,
        costPrice: numericValue,
        formattedCostPrice: formattedValue
      }));
    } else if (name === 'category') {
      // Find the selected category to get its label
      const selectedCategory = categories.find(cat => cat.value === value);
      setFormData(prev => ({
        ...prev,
        category: value,
        categoryLabel: selectedCategory?.label || ''
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleUploadMethodChange = (method: 'url' | 'file') => {
    setUploadMethod(method);
    // Reset image field when changing method
    if (method === 'file' && formData.image && !formData.image.startsWith('/uploads/')) {
      setFormData(prev => ({ ...prev, image: '' }));
    } else if (method === 'url' && formData.image && formData.image.startsWith('/uploads/')) {
      setFormData(prev => ({ ...prev, image: '' }));
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validasi ukuran file (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setErrors(prev => ({
        ...prev,
        image: 'Ukuran file terlalu besar (maksimal 5MB)'
      }));
      return;
    }

    // Validasi tipe file
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      setErrors(prev => ({
        ...prev,
        image: 'Tipe file tidak didukung (hanya JPG, PNG, GIF, WEBP)'
      }));
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch(`${config.apiUrl}/uploads`, {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const data = await response.json();
        setFormData(prev => ({ ...prev, image: data.fileUrl }));

        // Clear error
        if (errors.image) {
          setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors.image;
            return newErrors;
          });
        }
      } else {
        const errorData = await response.json();
        setErrors(prev => ({
          ...prev,
          image: errorData.error || 'Gagal mengunggah file'
        }));
      }
    } catch (error) {
      setErrors(prev => ({
        ...prev,
        image: 'Gagal mengunggah file'
      }));
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = () => {
    setFormData(prev => ({ ...prev, image: '' }));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validasi form
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nama produk harus diisi';
    }

    if (!formData.price || formData.price <= 0) {
      newErrors.price = 'Harga jual harus lebih dari 0';
    }

    if (!formData.image) {
      newErrors.image = 'Gambar produk harus diisi';
    } else if (uploadMethod === 'url' && formData.image) {
      // Validate URL format
      const urlPattern = /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i;
      if (!urlPattern.test(formData.image)) {
        newErrors.image = 'URL gambar tidak valid. Pastikan URL berakhir dengan .jpg, .jpeg, .png, .gif, atau .webp';
      }
    }

    if (!formData.category) {
      newErrors.category = 'Kategori harus dipilih';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsSaving(true);

    try {
      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      const url = isEditMode
        ? `${config.apiUrl}/products/${id}`
        : `${config.apiUrl}/products`;

      const method = isEditMode ? 'PUT' : 'POST';

      const response = await fetchWithSession(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name,
          price: formData.price,
          cost_price: formData.costPrice,
          image: formData.image,
          category: formData.category,
          category_label: formData.categoryLabel,
          user_id: userId
        })
      });

      if (response && response.ok) {
        const successMessage = isEditMode ? 'Produk berhasil diperbarui' : 'Produk berhasil ditambahkan';
        navigate('/products?success=' + encodeURIComponent(successMessage));
      } else {
        const errorData = await response?.json();
        if (errorData?.error) {
          alert(errorData.error);
        } else {
          alert('Terjadi kesalahan saat menyimpan produk');
        }
      }
    } catch (error) {
      alert('Terjadi kesalahan saat menyimpan produk');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center gap-3 mb-6">
        <Link to="/products" className="p-2 rounded-full hover:bg-neutral-100">
          <ArrowLeft size={20} />
        </Link>
        <h1 className="text-2xl font-bold text-primary-800">
          {isEditMode ? 'Edit Produk' : 'Tambah Produk Baru'}
        </h1>
      </div>

      <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
            <p className="mt-2 text-neutral-600">Memuat data...</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} autoComplete="off">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="mb-4">
                  <label htmlFor="name" className="block text-sm font-medium text-neutral-700 mb-1">
                    Nama Produk <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className={`w-full p-2 border ${errors.name ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                    autoComplete="off"
                  />
                  {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
                </div>

                <div className="mb-4">
                  <label htmlFor="price" className="block text-sm font-medium text-neutral-700 mb-1">
                    Harga <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-neutral-500">Rp</span>
                    </div>
                    <input
                      type="text"
                      id="price"
                      name="price"
                      value={formData.formattedPrice}
                      onChange={handleChange}
                      min="0"
                      step="500"
                      className={`w-full pl-10 p-2 border ${errors.price ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                      autoComplete="off"
                    />
                  </div>
                  {errors.price && <p className="mt-1 text-sm text-red-500">{errors.price}</p>}
                </div>

                <div className="mb-4">
                  <label htmlFor="costPrice" className="block text-sm font-medium text-neutral-700 mb-1">
                    Harga Modal <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-neutral-500">Rp</span>
                    </div>
                    <input
                      type="text"
                      id="costPrice"
                      name="costPrice"
                      value={formData.formattedCostPrice}
                      onChange={handleChange}
                      min="0"
                      step="500"
                      className={`w-full pl-10 p-2 border ${errors.costPrice ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                      autoComplete="off"
                    />
                  </div>
                  {errors.costPrice && <p className="mt-1 text-sm text-red-500">{errors.costPrice}</p>}
                </div>

                <div className="mb-4">
                  <label htmlFor="category" className="block text-sm font-medium text-neutral-700 mb-1">
                    Kategori <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    className={`w-full p-2 border ${errors.category ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                  >
                    <option value="">Pilih Kategori</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                  {errors.category && <p className="mt-1 text-sm text-red-500">{errors.category}</p>}
                </div>
              </div>

              <div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Gambar Produk <span className="text-red-500">*</span>
                  </label>

                  <div className="flex gap-4 mb-3">
                    <button
                      type="button"
                      onClick={() => handleUploadMethodChange('url')}
                      className={`flex-1 py-2 px-4 rounded-md ${uploadMethod === 'url'
                        ? 'bg-primary-100 text-primary-700 border border-primary-300'
                        : 'bg-neutral-100 text-neutral-700 border border-neutral-300'
                        }`}
                    >
                      URL Gambar
                    </button>
                    <button
                      type="button"
                      onClick={() => handleUploadMethodChange('file')}
                      className={`flex-1 py-2 px-4 rounded-md ${uploadMethod === 'file'
                        ? 'bg-primary-100 text-primary-700 border border-primary-300'
                        : 'bg-neutral-100 text-neutral-700 border border-neutral-300'
                        }`}
                    >
                      Upload File
                    </button>
                  </div>

                  {uploadMethod === 'url' ? (
                    <div>
                      <input
                        type="text"
                        id="image"
                        name="image"
                        value={formData.image}
                        onChange={handleChange}
                        className={`w-full p-2 border ${errors.image ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                        placeholder="https://example.com/image.jpg"
                        autoComplete="off"
                      />
                      <p className="mt-1 text-xs text-neutral-500">
                        Masukkan URL gambar dari internet
                      </p>
                    </div>
                  ) : (
                    <div>
                      <div className="flex items-center gap-2">
                        <input
                          type="file"
                          id="file-upload"
                          ref={fileInputRef}
                          onChange={handleFileChange}
                          accept="image/jpeg,image/png,image/gif,image/webp"
                          className="hidden"
                        />
                        <button
                          type="button"
                          onClick={() => fileInputRef.current?.click()}
                          className={`flex items-center gap-2 px-4 py-2 border ${errors.image ? 'border-red-500' : 'border-neutral-300'
                            } rounded-md hover:bg-neutral-50`}
                          disabled={isUploading}
                        >
                          {isUploading ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary-500 border-t-transparent"></div>
                              <span>Uploading...</span>
                            </>
                          ) : (
                            <>
                              <Upload size={18} />
                              <span>Pilih File</span>
                            </>
                          )}
                        </button>
                        {formData.image && (
                          <button
                            type="button"
                            onClick={handleRemoveImage}
                            className="p-2 text-red-500 hover:bg-red-50 rounded-full"
                          >
                            <X size={18} />
                          </button>
                        )}
                      </div>
                      <p className="mt-1 text-xs text-neutral-500">
                        Format: JPG, PNG, GIF, WEBP. Maks: 5MB
                      </p>
                    </div>
                  )}

                  {errors.image && <p className="mt-1 text-sm text-red-500">{errors.image}</p>}

                  {formData.image && (
                    <div className="mt-3">
                      <p className="text-sm font-medium text-neutral-700 mb-1">Preview:</p>
                      <div className="relative w-32 h-32 border border-neutral-200 rounded-md overflow-hidden">
                        <img
                          src={(() => {
                            if (!formData.image) {
                              return 'https://via.placeholder.com/150?text=No+Image';
                            }
                            if (formData.image.startsWith('/uploads/')) {
                              return `${config.imageBaseUrl}${formData.image}`;
                            }
                            if (formData.image.startsWith('http://') || formData.image.startsWith('https://')) {
                              return formData.image;
                            }
                            return `${config.imageBaseUrl}${formData.image}`;
                          })()}
                          alt="Preview"
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.src = 'https://via.placeholder.com/150?text=Error';
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <Link
                to="/products"
                className="px-4 py-2 border border-neutral-300 rounded-md text-neutral-700 hover:bg-neutral-50"
              >
                Batal
              </Link>
              <button
                type="submit"
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 flex items-center gap-2"
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    <span>Menyimpan...</span>
                  </>
                ) : (
                  <>
                    <Save size={18} />
                    <span>Simpan</span>
                  </>
                )}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default ProductForm;






