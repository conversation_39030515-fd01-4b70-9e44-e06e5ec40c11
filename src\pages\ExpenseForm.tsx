import React, { useState, useEffect } from 'react';
import { ArrowLeft, Save } from 'lucide-react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import config from '../config';
import { fetchWithSession, getCurrentUser } from '../utils/api';

interface Branch {
  id: number;
  name: string;
}

interface ExpenseFormData {
  description: string;
  amount: number;
  formattedAmount: string;
  category: string;
  date: string;
  branch_id: string;
}

const ExpenseForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);

  const [formData, setFormData] = useState<ExpenseFormData>({
    description: '',
    amount: 0,
    formattedAmount: '',
    category: '',
    date: new Date().toISOString().split('T')[0],
    branch_id: '',
  });

  const [branches, setBranches] = useState<Branch[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const formatNumberInput = (value: string | number): string => {
    // Handle null, undefined, or empty values
    if (value === null || value === undefined || value === '') {
      return '';
    }

    // Convert to number first to handle decimal strings properly
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;

    // If it's not a valid number, return empty string
    if (isNaN(numericValue)) {
      return '';
    }

    // Convert to integer (remove decimal places)
    const integerValue = Math.floor(numericValue);

    // Convert to string and format with thousands separator
    return integerValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  useEffect(() => {
    const user = getCurrentUser();
    setCurrentUser(user);
  }, []);

  useEffect(() => {
    if (currentUser) {
      fetchCategories();
      if (currentUser.role === 'admin') {
        fetchBranches();
      }
      if (isEdit && id) {
        fetchExpense(id);
      }
    }
  }, [currentUser, isEdit, id]);

  const fetchBranches = async () => {
    if (!currentUser) return;

    try {
      const response = await fetchWithSession(`${config.apiUrl}/branches?user_id=${currentUser.id}&user_type=${currentUser.user_type || currentUser.role}`);
      if (response?.ok) {
        const data = await response.json();
        setBranches(data);
      }
    } catch (error) {
      console.error('Error fetching branches:', error);
    }
  };

  const fetchCategories = async () => {
    if (!currentUser) return;

    try {
      const response = await fetchWithSession(`${config.apiUrl}/expenses/categories/list?user_id=${currentUser.id}&user_type=${currentUser.user_type || currentUser.role}`);
      if (response?.ok) {
        const data = await response.json();
        setCategories(data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchExpense = async (expenseId: string) => {
    if (!currentUser) return;

    try {
      const response = await fetchWithSession(`${config.apiUrl}/expenses/${expenseId}?user_id=${currentUser.id}&user_type=${currentUser.user_type || currentUser.role}`);
      if (response?.ok) {
        const expense = await response.json();
        const amount = Math.floor(parseFloat(expense.amount || '0'));

        // Handle date parsing - MySQL returns datetime as 'YYYY-MM-DD HH:MM:SS'
        // We need to extract just the date part (YYYY-MM-DD)
        let dateValue = '';
        if (expense.date) {
          // Handle both ISO format (with T) and MySQL format (with space)
          dateValue = expense.date.split('T')[0].split(' ')[0];
        }

        setFormData({
          description: expense.description,
          amount: amount,
          formattedAmount: formatNumberInput(amount),
          category: expense.category || '',
          date: dateValue,
          branch_id: expense.branch_id?.toString() || '',
        });
      } else {
        alert('Pengeluaran tidak ditemukan');
        navigate('/expenses');
      }
    } catch (error) {
      console.error('Error fetching expense:', error);
      alert('Terjadi kesalahan saat mengambil data pengeluaran');
      navigate('/expenses');
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.description.trim()) {
      newErrors.description = 'Deskripsi harus diisi';
    }

    if (formData.amount <= 0) {
      newErrors.amount = 'Jumlah harus diisi dan lebih dari 0';
    }

    if (!formData.date) {
      newErrors.date = 'Tanggal harus diisi';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentUser) {
      alert('User tidak ditemukan. Silakan login ulang.');
      return;
    }

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const submitData = {
        description: formData.description.trim(),
        amount: formData.amount,
        category: formData.category.trim() || null,
        date: formData.date,
        branch_id: formData.branch_id ? Number(formData.branch_id) : null,
        user_id: currentUser.id,
        user_type: currentUser.user_type || currentUser.role, // Use user_type from session for precise table identification
      };

      const url = isEdit
        ? `${config.apiUrl}/expenses/${id}`
        : `${config.apiUrl}/expenses`;

      const method = isEdit ? 'PUT' : 'POST';

      const response = await fetchWithSession(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      if (response?.ok) {
        const successMessage = isEdit ? 'Pengeluaran berhasil diperbarui' : 'Pengeluaran berhasil ditambahkan';
        navigate('/expenses?success=' + encodeURIComponent(successMessage));
      } else {
        const errorData = await response?.json();
        alert(errorData.error || 'Terjadi kesalahan saat menyimpan pengeluaran');
      }
    } catch (error) {
      console.error('Error saving expense:', error);
      alert('Terjadi kesalahan saat menyimpan pengeluaran');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name === 'amount') {
      // Extract only numeric characters from input
      const numericValue = parseInt(value.replace(/\D/g, '')) || 0;
      // Format the numeric value for display
      const formattedValue = formatNumberInput(numericValue);
      setFormData(prev => ({
        ...prev,
        amount: numericValue,
        formattedAmount: formattedValue
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  return (
    <div className="container mx-auto p-4">
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <Link to="/expenses" className="p-2 rounded-full hover:bg-neutral-100">
          <ArrowLeft size={20} />
        </Link>
        <h1 className="text-xl sm:text-2xl font-bold text-primary-800">
          {isEdit ? 'Edit Pengeluaran' : 'Tambah Pengeluaran'}
        </h1>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-neutral-700 mb-2">
              Deskripsi <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${errors.description ? 'border-red-500' : 'border-neutral-300'
                }`}
              placeholder="Masukkan deskripsi pengeluaran..."
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description}</p>
            )}
          </div>

          {/* Amount */}
          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-neutral-700 mb-2">
              Jumlah <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-neutral-500 sm:text-sm">Rp</span>
              </div>
              <input
                type="text"
                id="amount"
                name="amount"
                value={formData.formattedAmount}
                onChange={handleInputChange}
                className={`w-full pl-12 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${errors.amount ? 'border-red-500' : 'border-neutral-300'
                  }`}
                placeholder="0"
                autoComplete="off"
              />
            </div>
            {errors.amount && (
              <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
            )}
          </div>

          {/* Category */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-neutral-700 mb-2">
              Kategori
            </label>
            <input
              type="text"
              id="category"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              list="categories"
              className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="Masukkan atau pilih kategori..."
            />
            <datalist id="categories">
              {categories.map(category => (
                <option key={category} value={category} />
              ))}
            </datalist>
          </div>

          {/* Date */}
          <div>
            <label htmlFor="date" className="block text-sm font-medium text-neutral-700 mb-2">
              Tanggal <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              id="date"
              name="date"
              value={formData.date}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${errors.date ? 'border-red-500' : 'border-neutral-300'
                }`}
            />
            {errors.date && (
              <p className="mt-1 text-sm text-red-600">{errors.date}</p>
            )}
          </div>

          {/* Branch - Only for admin */}
          {currentUser?.role === 'admin' && (
            <div>
              <label htmlFor="branch_id" className="block text-sm font-medium text-neutral-700 mb-2">
                Cabang
              </label>
              <select
                id="branch_id"
                name="branch_id"
                value={formData.branch_id}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">Pilih Cabang (Opsional)</option>
                {branches.map(branch => (
                  <option key={branch.id} value={branch.id.toString()}>
                    {branch.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end gap-3 pt-6">
            <Link
              to="/expenses"
              className="px-6 py-2 text-neutral-600 border border-neutral-300 rounded-lg hover:bg-neutral-50 transition-colors"
            >
              Batal
            </Link>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center gap-2 px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
              ) : (
                <Save size={18} />
              )}
              <span>{loading ? 'Menyimpan...' : 'Simpan'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ExpenseForm;
