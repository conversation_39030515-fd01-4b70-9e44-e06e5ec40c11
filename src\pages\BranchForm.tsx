import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import config from '../config';
import { Branch } from '../types';

interface BranchFormData {
  name: string;
  address: string;
  phone: string;
  manager: string;
  isActive: boolean;
  email: string;
  password: string;
}

const BranchForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = id !== undefined;

  const [formData, setFormData] = useState<BranchFormData>({
    name: '',
    address: '',
    phone: '',
    manager: '',
    isActive: true,
    email: '',
    password: ''
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Tambahkan state untuk menyimpan data cabang asli
  const [originalBranch, setOriginalBranch] = useState<Branch | null>(null);

  useEffect(() => {
    if (isEditMode && id) {
      const fetchBranchData = async () => {
        setIsLoading(true);
        try {
          const response = await fetch(`${config.apiUrl}/branches/${id}`);
          if (response.ok) {
            const data = await response.json();
            setOriginalBranch(data); // Simpan data asli

            // Dapatkan user dari sessionStorage
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser') || '{}');
            const userId = currentUser.id; // Ambil ID user yang sedang login

            // Validasi apakah user yang mengedit sama dengan user yang membuat cabang
            if (data.user_id && data.user_id !== userId) {
              alert('Anda tidak memiliki izin untuk mengedit cabang ini');
              navigate('/branches');
              return;
            }

            setFormData({
              name: data.name,
              address: data.address,
              phone: data.phone,
              manager: data.manager,
              isActive: data.is_active === 1, // Konversi is_active ke isActive
              email: data.email,
              password: ''
            });
          } else {
            navigate('/branches');
          }
        } catch (error) {
          navigate('/branches');
        } finally {
          setIsLoading(false);
        }
      };

      fetchBranchData();
    }
  }, [isEditMode, id, navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nama cabang harus diisi';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Alamat harus diisi';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Nomor telepon harus diisi';
    }

    if (!formData.manager.trim()) {
      newErrors.manager = 'Nama manager harus diisi';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email harus diisi';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format email tidak valid';
    }

    if (!isEditMode && !formData.password.trim()) {
      newErrors.password = 'Password harus diisi';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validasi form
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);

    try {
      const url = isEditMode
        ? `${config.apiUrl}/branches/${id}`
        : `${config.apiUrl}/branches`;

      const method = isEditMode ? 'PUT' : 'POST';

      // Dapatkan user dari sessionStorage
      const currentUser = JSON.parse(sessionStorage.getItem('currentUser') || '{}');
      const userId = currentUser.id; // Ambil ID user yang sedang login

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name,
          address: formData.address,
          phone: formData.phone,
          manager: formData.manager,
          is_active: formData.isActive ? 1 : 0, // Gunakan is_active bukan isActive
          email: formData.email,
          password: formData.password || undefined, // Only send if not empty
          user_id: userId // Tambahkan user_id
        })
      });

      if (response.ok) {
        const successMessage = isEditMode ? 'Cabang berhasil diperbarui' : 'Cabang berhasil ditambahkan';
        navigate('/branches?success=' + encodeURIComponent(successMessage));
      } else {
        const errorData = await response.json();
        if (errorData.error) {
          alert(errorData.error);
        } else {
          alert('Terjadi kesalahan saat menyimpan data');
        }
      }
    } catch (error) {
      alert('Terjadi kesalahan saat menyimpan data');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center gap-3 mb-6">
        <Link to="/branches" className="p-2 rounded-full hover:bg-neutral-100">
          <ArrowLeft size={20} />
        </Link>
        <h1 className="text-2xl font-bold text-primary-800">
          {isEditMode ? 'Edit Cabang' : 'Tambah Cabang Baru'}
        </h1>
      </div>

      <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
            <p className="mt-2 text-neutral-600">Memuat data...</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} autoComplete="off">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="mb-4">
                  <label htmlFor="name" className="block text-sm font-medium text-neutral-700 mb-1">
                    Nama Cabang <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className={`w-full p-2 border ${errors.name ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                    autoComplete="off"
                  />
                  {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
                </div>

                <div className="mb-4">
                  <label htmlFor="address" className="block text-sm font-medium text-neutral-700 mb-1">
                    Alamat <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    rows={3}
                    className={`w-full p-2 border ${errors.address ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                    autoComplete="off"
                  />
                  {errors.address && <p className="mt-1 text-sm text-red-500">{errors.address}</p>}
                </div>

                <div className="mb-4">
                  <label htmlFor="phone" className="block text-sm font-medium text-neutral-700 mb-1">
                    Telepon <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className={`w-full p-2 border ${errors.phone ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                    autoComplete="off"
                  />
                  {errors.phone && <p className="mt-1 text-sm text-red-500">{errors.phone}</p>}
                </div>
              </div>

              <div>
                <div className="mb-4">
                  <label htmlFor="manager" className="block text-sm font-medium text-neutral-700 mb-1">
                    Manager <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="manager"
                    name="manager"
                    value={formData.manager}
                    onChange={handleChange}
                    className={`w-full p-2 border ${errors.manager ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                    autoComplete="off"
                  />
                  {errors.manager && <p className="mt-1 text-sm text-red-500">{errors.manager}</p>}
                </div>

                <div className="mb-4">
                  <label htmlFor="isActive" className="flex items-center">
                    <input
                      type="checkbox"
                      id="isActive"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleChange}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
                    />
                    <span className="ml-2 text-sm text-neutral-700">Cabang Aktif</span>
                  </label>
                </div>
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="email" className="block text-sm font-medium text-neutral-700 mb-1">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full p-2 border ${errors.email ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                autoComplete="off"
              />
              {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
            </div>

            <div className="mb-4">
              <label htmlFor="password" className="block text-sm font-medium text-neutral-700 mb-1">
                Password {!isEditMode && <span className="text-red-500">*</span>}
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className={`w-full p-2 border ${errors.password ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                autoComplete="off"
              />
              {errors.password && <p className="mt-1 text-sm text-red-500">{errors.password}</p>}
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <Link
                to="/branches"
                className="px-4 py-2 border border-neutral-300 rounded-md hover:bg-neutral-50"
              >
                Batal
              </Link>
              <button
                type="submit"
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                disabled={isLoading}
              >
                {isLoading ? 'Menyimpan...' : isEditMode ? 'Simpan Perubahan' : 'Tambah Cabang'}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default BranchForm;









