import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Save } from 'lucide-react';
import config from '../config';
import { fetchWithSession, getCurrentUser, buildApiUrl } from '../utils/api';

interface CategoryFormData {
  id?: string;
  label: string;
  value: string;
  icon: string;
  count?: number;
}

const EMOJI_OPTIONS = [
  // Makanan <PERSON>a
  '🍽️', '🍞', '🥐', '🥖', '🥨', '🥯', '🍳', '🥚', '🧀', '🥓',
  '🥩', '🍗', '🍖', '🌭', '🍔', '🍟', '🍕', '🥪', '🌮', '🌯',
  '🥙', '🧆', '🥘', '🍲', '🥣', '🥗', '🍱', '🍘', '🍙', '🍚',
  '🍛', '🍜', '🍝', '🍠', '🍢', '🍣', '🍤', '🍥', '🍡', '🥟',
  '🥠', '🥡', '🦪', '🦞', '🦀', '🐟', '🐠', '🍖', '🥩', '🍗',

  // <PERSON><PERSON><PERSON> & Dessert
  '🍩', '🍪', '🎂', '🧁', '🥮', '🍰', '🍫', '🍬', '🍭', '🍮',
  '🍯', '🍦', '🍧', '🍨', '🧊', '🍿', '🥨', '🥯', '🧈', '🧂',
  '🥫', '🍯', '🥜', '🌰', '🥥', '🍪', '🧁', '🎂', '🍰', '🍫',

  // Minuman
  '🍺', '🍻', '🥂', '🍷', '🥃', '🍸', '🍹', '🧃', '🧉', '🥤',
  '☕', '🍵', '🍶', '🥛', '🍼', '🧋', '🥤', '🧊', '🍾', '🍷',
  '🍸', '🍹', '🍺', '🍻', '🥂', '🥃', '☕', '🍵', '🧃', '🥛',

  // Buah-buahan
  '🍓', '🍇', '🍈', '🍉', '🍊', '🍋', '🍌', '🍍', '🥭', '🍎',
  '🍏', '🍐', '🍑', '🍒', '🥝', '🥥', '🫐', '🍈', '🍉', '🍊',
  '🍋', '🍌', '🍍', '🥭', '🍎', '🍏', '🍐', '🍑', '🍒', '🥝',

  // Sayuran & Bumbu
  '🍅', '🍆', '🌽', '🌶️', '🍄', '🥑', '🥒', '🥬', '🥦', '🥔',
  '🧄', '🧅', '🥕', '🌱', '🥜', '🫘', '🌿', '🍃', '🥒', '🥬',
  '🥦', '🥔', '🧄', '🧅', '🥕', '🌶️', '🫑', '🥒', '🍅', '🍆',

  // Peralatan Makan & Dapur
  '🍴', '🥄', '🔪', '🥢', '🍽️', '🥣', '🥛', '🍶', '🧊', '🔥',
  '⏰', '🧑‍🍳', '👨‍🍳', '👩‍🍳', '🍳', '🥘', '🍲', '🫖', '☕', '🍵'
];

const CategoryForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = id !== undefined;

  const [formData, setFormData] = useState<CategoryFormData>({
    label: '',
    value: '',
    icon: '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [iconType, setIconType] = useState<'emoji' | 'upload'>('emoji');
  const [uploadedIcon, setUploadedIcon] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    if (isEditMode) {
      const fetchCategory = async () => {
        setIsLoading(true);
        try {
          const url = buildApiUrl(`${config.apiUrl}/categories/${id}`);
          const response = await fetchWithSession(url);
          if (response && response.ok) {
            const categoryData = await response.json();
            setFormData({
              label: categoryData.label,
              value: categoryData.value,
              icon: categoryData.icon,
              count: categoryData.count || 0
            });

            // Detect icon type
            if (categoryData.icon && (categoryData.icon.startsWith('/uploads/') || categoryData.icon.startsWith('http'))) {
              setIconType('upload');
              setUploadedIcon(categoryData.icon);
            } else {
              setIconType('emoji');
            }
          } else {
            navigate('/categories');
          }
        } catch (error) {
          navigate('/categories');
        } finally {
          setIsLoading(false);
        }
      };

      fetchCategory();
    }
  }, [id, isEditMode, navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === 'label') {
      // Auto-generate value from label (lowercase, no spaces)
      const generatedValue = value.toLowerCase().replace(/\s+/g, '_');
      setFormData(prev => ({
        ...prev,
        [name]: value,
        value: generatedValue
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleIconSelect = (icon: string) => {
    setFormData(prev => ({ ...prev, icon }));
    setIconType('emoji');
    setUploadedIcon(null);

    // Clear error for icon
    if (errors.icon) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.icon;
        return newErrors;
      });
    }
  };

  const handleIconUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setErrors(prev => ({ ...prev, icon: 'File harus berupa gambar' }));
      return;
    }

    // Validate file size (max 1MB for icons)
    if (file.size > 1 * 1024 * 1024) {
      setErrors(prev => ({ ...prev, icon: 'Ukuran file maksimal 1MB' }));
      return;
    }

    setIsUploading(true);
    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('icon', file);

      // Upload to server
      const response = await fetchWithSession(`${config.apiUrl}/uploads/icon`, {
        method: 'POST',
        body: formData,
      });

      if (response && response.ok) {
        const result = await response.json();
        const iconUrl = result.iconUrl;

        setUploadedIcon(iconUrl);
        setFormData(prev => ({ ...prev, icon: iconUrl }));
        setIconType('upload');

        // Clear error for icon
        if (errors.icon) {
          setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors.icon;
            return newErrors;
          });
        }
      } else {
        const errorData = await response?.json();
        setErrors(prev => ({ ...prev, icon: errorData?.error || 'Gagal mengupload icon' }));
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, icon: 'Gagal mengupload icon' }));
    } finally {
      setIsUploading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.label?.trim()) {
      newErrors.label = 'Nama kategori harus diisi';
    }

    if (!formData.value?.trim()) {
      newErrors.value = 'Kode kategori harus diisi';
    } else if (!/^[a-z0-9_]+$/.test(formData.value)) {
      newErrors.value = 'Kode kategori hanya boleh berisi huruf kecil, angka, dan underscore';
    }

    if (!formData.icon) {
      newErrors.icon = 'Icon harus dipilih';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSaving(true);

    try {
      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      const url = isEditMode
        ? `${config.apiUrl}/categories/${id}`
        : `${config.apiUrl}/categories`;

      const method = isEditMode ? 'PUT' : 'POST';

      const response = await fetchWithSession(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          value: formData.value,
          label: formData.label,
          icon: formData.icon,
          count: formData.count || 0,
          user_id: userId
        })
      });

      if (response && response.ok) {
        const successMessage = isEditMode ? 'Kategori berhasil diperbarui' : 'Kategori berhasil ditambahkan';
        navigate('/categories?success=' + encodeURIComponent(successMessage));
      } else {
        const errorData = await response?.json();
        if (errorData?.error) {
          alert(errorData.error);
        } else {
          alert('Terjadi kesalahan saat menyimpan kategori');
        }
      }
    } catch (error) {
      alert('Terjadi kesalahan saat menyimpan kategori');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center gap-3 mb-6">
        <Link to="/categories" className="p-2 rounded-full hover:bg-neutral-100">
          <ArrowLeft size={20} />
        </Link>
        <h1 className="text-2xl font-bold text-primary-800">
          {isEditMode ? 'Edit Kategori' : 'Tambah Kategori Baru'}
        </h1>
      </div>

      <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
            <p className="mt-2 text-neutral-600">Memuat data...</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} autoComplete="off">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="mb-4">
                  <label htmlFor="label" className="block text-sm font-medium text-neutral-700 mb-1">
                    Nama Kategori <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="label"
                    name="label"
                    value={formData.label}
                    onChange={handleChange}
                    className={`w-full p-2 border ${errors.label ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                    autoComplete="off"
                  />
                  {errors.label && <p className="mt-1 text-sm text-red-500">{errors.label}</p>}
                </div>

                <div className="mb-4">
                  <label htmlFor="value" className="block text-sm font-medium text-neutral-700 mb-1">
                    Kode Kategori <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="value"
                    name="value"
                    value={formData.value}
                    onChange={handleChange}
                    className={`w-full p-2 border ${errors.value ? 'border-red-500' : 'border-neutral-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500`}
                    autoComplete="off"
                  />
                  <p className="mt-1 text-xs text-neutral-500">
                    Kode kategori hanya boleh berisi huruf kecil, angka, dan underscore (_)
                  </p>
                  {errors.value && <p className="mt-1 text-sm text-red-500">{errors.value}</p>}
                </div>
              </div>

              <div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Icon <span className="text-red-500">*</span>
                  </label>

                  {/* Icon Preview */}
                  <div className="flex items-center mb-3">
                    <div className={`w-12 h-12 flex items-center justify-center border border-neutral-300 rounded-md mr-2 ${iconType === 'upload' ? 'p-1' : 'text-2xl'}`}>
                      {iconType === 'upload' && (formData.icon?.startsWith('/uploads/') || formData.icon?.startsWith('http')) ? (
                        <img
                          src={formData.icon?.startsWith('/uploads/') ? `${config.imageBaseUrl}${formData.icon}` : formData.icon}
                          alt="Custom icon"
                          className="w-full h-full object-cover rounded"
                        />
                      ) : (
                        formData.icon
                      )}
                    </div>
                    <span className="text-sm text-neutral-600">
                      {iconType === 'upload' ? 'Icon custom' : 'Icon emoji'}
                    </span>
                  </div>
                  {errors.icon && <p className="mt-1 text-sm text-red-500">{errors.icon}</p>}

                  {/* Icon Type Selector */}
                  <div className="flex gap-2 mb-3">
                    <button
                      type="button"
                      onClick={() => setIconType('emoji')}
                      className={`px-3 py-1 text-sm rounded-md transition-colors ${iconType === 'emoji'
                        ? 'bg-primary-100 text-primary-700 border border-primary-300'
                        : 'bg-neutral-100 text-neutral-700 border border-neutral-300 hover:bg-neutral-200'
                        }`}
                    >
                      Emoji
                    </button>
                    <button
                      type="button"
                      onClick={() => setIconType('upload')}
                      className={`px-3 py-1 text-sm rounded-md transition-colors ${iconType === 'upload'
                        ? 'bg-primary-100 text-primary-700 border border-primary-300'
                        : 'bg-neutral-100 text-neutral-700 border border-neutral-300 hover:bg-neutral-200'
                        }`}
                    >
                      Upload Custom
                    </button>
                  </div>

                  {/* Emoji Selection */}
                  {iconType === 'emoji' && (
                    <div className="grid grid-cols-8 gap-2 mt-3 max-h-48 overflow-y-auto p-2 border border-neutral-200 rounded-md">
                      {EMOJI_OPTIONS.map((emoji, index) => (
                        <button
                          key={index}
                          type="button"
                          onClick={() => handleIconSelect(emoji)}
                          className={`w-10 h-10 text-xl flex items-center justify-center rounded-md transition-colors ${formData.icon === emoji && iconType === 'emoji'
                            ? 'bg-primary-100 border-2 border-primary-500'
                            : 'hover:bg-neutral-100'
                            }`}
                        >
                          {emoji}
                        </button>
                      ))}
                    </div>
                  )}

                  {/* Upload Custom Icon */}
                  {iconType === 'upload' && (
                    <div className="mt-3 p-4 border border-neutral-200 rounded-md">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleIconUpload}
                        className="hidden"
                        id="icon-upload"
                        disabled={isUploading}
                      />
                      <label
                        htmlFor="icon-upload"
                        className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-md cursor-pointer transition-colors ${isUploading
                          ? 'border-neutral-300 bg-neutral-50 cursor-not-allowed'
                          : 'border-neutral-300 hover:border-primary-400 hover:bg-primary-50'
                          }`}
                      >
                        {isUploading ? (
                          <div className="flex flex-col items-center">
                            <div className="inline-block animate-spin rounded-full h-6 w-6 border-4 border-primary-500 border-t-transparent mb-2"></div>
                            <span className="text-sm text-neutral-600">Mengupload...</span>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center">
                            <svg className="w-8 h-8 text-neutral-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <span className="text-sm text-neutral-600">Klik untuk upload icon</span>
                            <span className="text-xs text-neutral-500 mt-1">PNG, JPG, GIF, WEBP (max 1MB)</span>
                          </div>
                        )}
                      </label>
                    </div>
                  )}
                </div>

                <div className="mt-6">
                  <button
                    type="submit"
                    className="w-full flex items-center justify-center gap-2 bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <div className="inline-block animate-spin rounded-full h-5 w-5 border-4 border-white border-t-transparent"></div>
                    ) : (
                      <>
                        <Save size={18} />
                        <span>{isEditMode ? 'Simpan Perubahan' : 'Tambah Kategori'}</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default CategoryForm;



