import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { Plus, Search, Edit, Trash2, Check, X, ArrowLeft, AlertCircle } from 'lucide-react';
import config from '../config';

interface Branch {
  id: string;
  name: string;
  address: string;
  phone: string;
  manager: string;
  is_active: boolean;
  email: string;
  userId: string;
}

const BranchManagement: React.FC = () => {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [branchToDelete, setBranchToDelete] = useState<Branch | null>(null);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const showSuccess = (message: string) => {
    setAlertMessage(message);
    setShowSuccessAlert(true);
    setTimeout(() => {
      setShowSuccessAlert(false);
    }, 3000);
  };

  const showError = (message: string) => {
    setAlertMessage(message);
    setShowErrorAlert(true);
    setTimeout(() => {
      setShowErrorAlert(false);
    }, 3000);
  };

  // Check for success message from URL parameters
  useEffect(() => {
    const successMessage = searchParams.get('success');
    if (successMessage) {
      showSuccess(successMessage);
      // Remove the success parameter from URL
      setSearchParams({});
    }
  }, [searchParams, setSearchParams]);

  // Fetch branches from API
  useEffect(() => {
    const fetchBranches = async () => {
      setIsLoading(true);
      try {
        // Dapatkan user dari sessionStorage
        const currentUser = JSON.parse(sessionStorage.getItem('currentUser') || '{}');
        const userId = currentUser.id; // Ambil ID user yang sedang login

        // Tambahkan user_id sebagai query parameter
        const response = await fetch(`${config.apiUrl}/branches?user_id=${userId}`);
        if (response.ok) {
          const data = await response.json();
          // Data is already sorted by server
          setBranches(data);
        } else {
          setBranches([]);
        }
      } catch (error) {
        setBranches([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBranches();
  }, []);

  const filteredBranches = branches.filter(branch =>
    branch.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    branch.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
    branch.manager.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleDeleteClick = (branch: Branch) => {
    setBranchToDelete(branch);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (branchToDelete) {
      try {
        // Dapatkan user dari sessionStorage
        const currentUser = JSON.parse(sessionStorage.getItem('currentUser') || '{}');
        const userId = currentUser.id; // Ambil ID user yang sedang login

        // Tambahkan user_id sebagai query parameter
        const response = await fetch(`${config.apiUrl}/branches/${branchToDelete.id}?user_id=${userId}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          // Remove branch from state
          const updatedBranches = branches.filter(b => b.id !== branchToDelete.id);
          setBranches(updatedBranches);
          showSuccess('Cabang berhasil dihapus');
        } else {
          const errorData = await response.json();
          showError(errorData.error || 'Gagal menghapus cabang');
        }
      } catch (error) {
        showError('Terjadi kesalahan saat menghapus cabang');
      }

      setIsDeleteModalOpen(false);
      setBranchToDelete(null);
    }
  };

  const handleAddBranch = () => {
    navigate('/branch/new');
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-3">
        <div className="flex items-center gap-3">
          <Link to="/dashboard-summary" className="p-2 rounded-full hover:bg-neutral-100">
            <ArrowLeft size={20} />
          </Link>
          <h1 className="text-2xl font-bold text-primary-800">Manajemen Cabang</h1>
        </div>
        <button
          onClick={handleAddBranch}
          className="flex items-center justify-center gap-2 bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 w-full sm:w-auto"
        >
          <Plus size={18} />
          <span>Tambah Cabang</span>
        </button>
      </div>

      {/* Success Alert */}
      {showSuccessAlert && (
        <div className="bg-green-100 border border-green-200 text-green-800 rounded-lg p-4 mb-6 flex items-center">
          <Check size={20} className="mr-2" />
          <span>{alertMessage}</span>
        </div>
      )}

      {/* Error Alert */}
      {showErrorAlert && (
        <div className="bg-red-100 border border-red-200 text-red-800 rounded-lg p-4 mb-6 flex items-center">
          <AlertCircle size={20} className="mr-2" />
          <span>{alertMessage}</span>
        </div>
      )}

      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="p-4 border-b">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-neutral-400" />
            </div>
            <input
              type="text"
              placeholder="Cari cabang..."
              className="pl-10 p-2 border border-neutral-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              autoComplete="off"
            />
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
            <p className="mt-2 text-neutral-600">Memuat data...</p>
          </div>
        ) : filteredBranches.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-neutral-600">Tidak ada cabang yang ditemukan</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-neutral-200">
              <thead className="bg-neutral-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Nama Cabang</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Alamat</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Telepon</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Manager</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">Aksi</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-neutral-200">
                {filteredBranches.map((branch) => (
                  <tr key={branch.id} className="hover:bg-neutral-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-neutral-900">{branch.name}</div>
                      <div className="text-sm text-neutral-500">{branch.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-neutral-700">{branch.address}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-neutral-700">{branch.phone}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-neutral-700">{branch.manager}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {branch.is_active ? (
                        <span className="inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <Check size={12} />
                          Aktif
                        </span>
                      ) : (
                        <span className="inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 text-neutral-800">
                          <X size={12} />
                          Tidak Aktif
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end gap-2">
                        <Link
                          to={`/branch/edit/${branch.id}`}
                          className="text-primary-600 hover:text-primary-900 p-1 rounded-full hover:bg-primary-50"
                        >
                          <Edit size={18} />
                        </Link>
                        <button
                          onClick={() => handleDeleteClick(branch)}
                          className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50"
                        >
                          <Trash2 size={18} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-neutral-900 mb-4">Konfirmasi Hapus</h3>
            <p className="text-neutral-700 mb-6">
              Apakah Anda yakin ingin menghapus cabang <span className="font-semibold">{branchToDelete?.name}</span>? Tindakan ini tidak dapat dibatalkan.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 border border-neutral-300 rounded-md hover:bg-neutral-50"
              >
                Batal
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Hapus
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BranchManagement;







